<script lang="ts" setup>
import type { FormSubmitEvent } from "@nuxt/ui";
import { loginSchema } from "~/types/auth";
import type { LoginFormData } from "~/types/auth";

definePageMeta({
  layout: "auth",
});

useHead({
  title: "Login",
  meta: [
    {
      name: "description",
      content: "Login to your account",
    },
  ],
});

const { doLogin } = useAuth();
const toast = useToast();

const showPassword = ref(false);
const formState = reactive<LoginFormData>({
  email: "",
  password: "",
});

const onSubmit = async (event: FormSubmitEvent<LoginFormData>) => {
  try {
    const result = await doLogin(event.data);

    if (result?.success) {
      toast.add({
        title: "Success",
        description: result.message || "Login successful",
        color: "success",
      });

      // Optional: Redirect to dashboard page after successful login
      // await navigateTo('/');
    } else {
      toast.add({
        title: "Error",
        description: result?.message || "Login failed",
        color: "error",
      });
    }
  } catch (error: any) {
    // Handle different types of errors appropriately
    let errorMessage = "An unexpected error occurred";

    if (error.message) {
      errorMessage = error.message;
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.response?.problem) {
      errorMessage = `Network error: ${error.response.problem}`;
    }

    toast.add({
      title: "Error",
      description: errorMessage,
      color: "error",
    });
  }
};
</script>
<template>
  <UCard class="w-full max-w-sm">
    <div class="bg-brand-400 text-primary text-2xl font-bold">Login</div>
    <div class="mb-2 text-sm text-gray-500">
      Don't have an account?
      <span>
        <UButton
          color="neutral"
          variant="link"
          to="/auth/register"
          class="text-primary -ml-3 font-bold"
        >
          Register here
        </UButton>
      </span>
    </div>
    <UForm
      :schema="loginSchema"
      :state="formState"
      class="space-y-6"
      @submit="onSubmit"
    >
      <UFormField name="email">
        <UInput v-model="formState.email" placeholder="Email/ Username" />
      </UFormField>

      <UFormField name="password">
        <UInput
          v-model="formState.password"
          placeholder="Password"
          :type="showPassword ? 'text' : 'password'"
        >
          <template #trailing>
            <UButton
              color="neutral"
              variant="link"
              size="md"
              :icon="showPassword ? 'i-lucide-eye-off' : 'i-lucide-eye'"
              :aria-label="showPassword ? 'Hide password' : 'Show password'"
              :aria-pressed="showPassword"
              aria-controls="password"
              @click="showPassword = !showPassword"
            />
          </template>
        </UInput>
      </UFormField>
      <UButton type="submit" class="flex w-full justify-center text-white">
        Continue
      </UButton>
    </UForm>
  </UCard>
</template>
