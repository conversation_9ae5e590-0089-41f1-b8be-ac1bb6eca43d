import { create, type ApisauceInstance, type ApiResponse } from "apisauce";

/**
 * Custom API Error class that preserves the original response object
 * This ensures that error.response is always available, even for network errors
 */
export class ApiError extends Error {
  public response: ApiResponse<any>;
  public status: number | null;
  public problem: string | null;

  constructor(response: ApiResponse<any>, message?: string) {
    const errorMessage =
      message ||
      response.data?.message ||
      response.problem ||
      "An unexpected error occurred";

    super(errorMessage);
    this.name = "ApiError";
    this.response = response;
    this.status = response.status || null;
    this.problem = response.problem || null;

    // Maintain proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ApiError);
    }
  }
}

// Global variable to store the API client instance
let _apiClient: ApisauceInstance | null = null;

/**
 * Creates and configures the main API client using apisauce
 * The base URL is read from environment variables via NUXT runtime config
 * This function must be called within a NUXT context (composable, plugin, etc.)
 */
export function createApiClient(): ApisauceInstance {
  const config = useRuntimeConfig();

  // Create the apisauce instance with base configuration
  const api = create({
    baseURL: config.public.apiBaseUrl,
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
    timeout: 10000, // 10 seconds timeout
  });

  // Request interceptor - runs before every request
  api.addRequestTransform((request) => {
    // Add timestamp to requests for debugging
    if (import.meta.dev) {
      console.log(
        `🚀 API Request: ${request.method?.toUpperCase()} ${request.url}`,
      );
    }

    // You can add authentication headers here if needed
    // request.headers['Authorization'] = `Bearer ${token}`
  });

  // Response interceptor - runs after every response
  api.addResponseTransform((response) => {
    if (import.meta.dev) {
      console.log(
        `📡 API Response: ${response.status || "N/A"} ${response.config?.url}`,
      );
    }

    // Handle common error cases with better logging
    if (!response.ok) {
      console.error("API Error:", {
        status: response.status || null,
        problem: response.problem || "Unknown",
        data: response.data || null,
        url: response.config?.url || "Unknown URL",
        originalError: response.originalError || null,
      });
    }
  });

  return api;
}

/**
 * Get or create the API client instance
 * This ensures we only create one instance and reuse it
 */
export function getApiClient(): ApisauceInstance {
  if (!_apiClient) {
    _apiClient = createApiClient();
  }
  return _apiClient;
}

/**
 * Common API error handler
 * Use this to handle API errors consistently across the application
 * Throws ApiError which preserves the original response object
 */
export function handleApiError(response: ApiResponse<any>) {
  if (!response.ok) {
    throw new ApiError(response);
  }

  return response.data;
}

/**
 * Helper function to make GET requests with error handling
 */
export async function apiGet<T>(url: string, params?: object): Promise<T> {
  const client = getApiClient();
  const response = await client.get<T>(url, params);
  return handleApiError(response);
}

/**
 * Helper function to make POST requests with error handling
 */
export async function apiPost<T>(url: string, data?: any): Promise<T> {
  const client = getApiClient();
  const response = await client.post<T>(url, data);
  return handleApiError(response);
}

/**
 * Helper function to make PUT requests with error handling
 */
export async function apiPut<T>(url: string, data?: any): Promise<T> {
  const client = getApiClient();
  const response = await client.put<T>(url, data);
  return handleApiError(response);
}

/**
 * Helper function to make DELETE requests with error handling
 */
export async function apiDelete<T>(url: string): Promise<T> {
  const client = getApiClient();
  const response = await client.delete<T>(url);
  return handleApiError(response);
}
